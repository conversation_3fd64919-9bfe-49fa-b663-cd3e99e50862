"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export interface Comment {
  id: number;
  user: string;
  text: string;
  created_at: string;
}

export function useComments() {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchComments = async (projectId: string, phase_id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.get(
        `${API_ROUTES.PROJECT_COMMENTS.replace("{project_id}", projectId).replace("{phase_id}", phase_id)}`,
      );

      if (response.status === 200) {
        setComments(response.data);
      } else {
        setError("Failed to fetch comments");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch comments");
    } finally {
      setLoading(false);
    }
  };

  const addComment = async (
    projectId: string,
    phaseId: string,
    text: string,
  ) => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.post(
        `${API_ROUTES.UPLOAD_PROJECT_COMMENT.replace("{project_id}", projectId).replace("{phase_id}", phaseId)}`,
        { text },
      );

      if (response.status === 201) {
        setComments((prevComments) => [...prevComments, response.data]);
      } else {
        setError("Failed to add comment");
      }
    } catch (err: any) {
      setError(err.message || "Failed to add comment");
    } finally {
      setLoading(false);
    }
  };

  return { comments, loading, error, fetchComments, addComment };
}
