import React, { useEffect } from "react";
import {
  Card,
  CardBody,
  Avatar,
  Input,
  Button,
  Divider,
  Spacer,
} from "@heroui/react";

import { CommentList } from "./comment-list";

import { useComments } from "@/hooks/comments/useComments";

interface CommentsSectionProps {
  projectId: string;
  currentPhase: string;
}

export const CommentsSection: React.FC<CommentsSectionProps> = ({
  projectId,
  currentPhase,
}) => {
  const { comments, loading, error, fetchComments, addComment } = useComments();

  const [newComment, setNewComment] = React.useState("");
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const handleAddComment = () => {
    if (!newComment.trim()) return;

    setIsSubmitting(true);

    addComment(projectId, currentPhase, newComment)
      .then(() => {
        setNewComment("");
      })
      .catch((err) => {
        console.error("Error adding comment:", err);
      })
      .finally(() => {
        setIsSubmitting(false);
        fetchComments(projectId, currentPhase);
      });
  };

  useEffect(() => {
    if (currentPhase === "-1" || !currentPhase || !projectId) return;
    console.log("Fetching comments for phase:", currentPhase);

    fetchComments(projectId, currentPhase);
  }, [projectId, currentPhase]);

  if (currentPhase === "-1" || !currentPhase)
    return (
      <div className="text-center py-8 text-default-500">
        <p>No phase selected. Please select a phase to view comments.</p>
      </div>
    );

  return (
    <div className="space-y-6">
      <Card>
        <CardBody>
          <div>
            <div className="flex gap-4">
              <Avatar
                className="flex-shrink-0"
                size="md"
                src="/broggo_pfp.png"
              />
              <div className="flex-grow">
                <div className="flex gap-2">
                  <Input
                    fullWidth
                    className="flex-grow"
                    placeholder="Escriba el comentario..."
                    value={newComment}
                    variant="bordered"
                    onValueChange={setNewComment}
                  />
                  <Button
                    color="primary"
                    isDisabled={!newComment.trim()}
                    isLoading={isSubmitting}
                    onPress={handleAddComment}
                  >
                    Publicar
                  </Button>
                </div>
              </div>
            </div>

            <div>
              <Spacer y={4} />
              <Divider className="mb-4" />
              <CommentList comments={comments} />
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};
