import { gql } from "@apollo/client";

export const GET_ALL_PROJECTS = gql`
  query GetAllProjects {
    allProjects {
      id
      alias
      companyName
      implementationType
      implementer {
        name
      }
      backup {
        name
      }
      coordinator {
        name
      }
      team {
        name
      }
      startInitialDate
      startFinalDate
      collectionInitialDate
      collectionFinalDate
      migrationInitialDate
      migrationFinalDate
      testInitialDate
      testFinalDate
      goliveInitialDate
      goliveFinalDate
      incubadoraInitialDate
      incubadoraFinalDate
    }
  }
`;

export const GET_PROJECT = gql`
  query GetProject($id: Int!) {
    project(id: $id) {
      id
      lid
      alias
      companyName
      aggregator
      implementationType
      implementer1 {
        name
      }
      implementer2 {
        name
      }
      backup {
        name
      }
      coordinator {
        name
      }
      team {
        name
      }
      incubator {
        name
      }
      startInitialDate
      startFinalDate
      collectionInitialDate
      collectionFinalDate
      migrationInitialDate
      migrationFinalDate
      testInitialDate
      testFinalDate
      goliveInitialDate
      goliveFinalDate
      incubadoraInitialDate
      incubadoraFinalDate
      month1Test
      month2Test
    }
  }
`;

export const GET_ALL_PROJECTS_TABLE = gql`
  query GetAllProjectsTable {
    allProjects {
      id
      lid
      alias
      aggregator
      # implementationType
      implementer1 {
        firstName
        lastName
      }
      goliveFinalDate
    }
  }
`;

export const GET_ALL_PROJECTS_TIMETABLE = gql`
  query GetAllProjectsTimetable {
    allProjects {
      id
      lid
      alias
      aggregator
      implementationType
      implementer1 {
        firstName
        lastName
      }
      implementer2 {
        firstName
        lastName
      }
      startInitialDate
      goliveFinalDate
    }
  }
`;

export const CREATE_PROJECT = gql`
  mutation CreateProject($input: ProjectInput!) {
    createProject(input: $input) {
      project {
        id
        lid
        alias
        companyName
        aggregator
        implementationType
        startInitialDate
        startFinalDate
        collectionInitialDate
        collectionFinalDate
        migrationInitialDate
        migrationFinalDate
        testInitialDate
        testFinalDate
        month1Test
        month2Test
        goliveInitialDate
        goliveFinalDate
        implementer1 {
          id
          name
          email
        }
        implementer2 {
          id
          name
          email
        }
        backup {
          id
          name
          email
        }
        coordinator {
          id
          name
          email
        }
        team {
          id
          name
          code
        }
        incubator {
          id
          name
          email
        }
        template {
          id
        }
      }
    }
  }
`;

export const UPDATE_PROJECT_USERS = gql`
  mutation UpdateProjectUsers($id: Int!, $input: ProjectInput!) {
    updateProject(id: $id, input: $input) {
      project {
        id
        aggregator
        implementer1 {
          id
          name
          email
        }
        implementer2 {
          id
          name
          email
        }
        backup {
          id
          name
          email
        }
        coordinator {
          id
          name
          email
        }
        team {
          id
          name
          code
        }
        incubator {
          id
          name
          email
        }
      }
    }
  }
`;

export const GET_BASIC_PROJECT_INFO = gql`
  query GetBasicProject($id: Int!) {
    project(id: $id) {
      id
      lid
      alias
      companyName
      aggregator
    }
  }
`;

export const GET_ALL_BASIC_PROJECTS_INFO = gql`
  query GetAllBasicProjects {
    allProjects {
      id
      lid
      alias
      companyName
      aggregator
    }
  }
`;

export const GET_PROJECT_ALL_DATES = gql`
  query GetProject($id: Int!) {
    project(id: $id) {
      id
      lid
      alias
      companyName
      aggregator
      # implementationType
      implementer1 {
        name
      }
      implementer2 {
        name
      }
      backup {
        name
      }
      coordinator {
        name
      }
      team {
        name
      }
      incubator {
        name
      }
      startInitialDate
      startFinalDate
      startRealInitialDate
      startRealFinalDate
      collectionInitialDate
      collectionFinalDate
      collectionRealInitialDate
      collectionRealFinalDate
      migrationInitialDate
      migrationFinalDate
      migrationRealInitialDate
      migrationRealFinalDate
      testInitialDate
      testFinalDate
      testRealInitialDate
      testRealFinalDate
      goliveInitialDate
      goliveFinalDate
      goliveRealInitialDate
      goliveRealFinalDate
      incubadoraInitialDate
      incubadoraFinalDate
      incubadoraRealInitialDate
      incubadoraRealFinalDate
      month1Test
      month2Test
    }
  }
`;
