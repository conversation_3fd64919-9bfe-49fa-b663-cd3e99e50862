"use client";

import { useState } from "react";

import { UpdateReportData } from "@/types/report";
import { ReportsService } from "@/services/reports";

export function useReportEdit() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateReport = async (
    id: number,
    updateData: UpdateReportData,
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      setLoading(true);
      setError(null);

      await ReportsService.updateReport(id, updateData);

      return { success: true };
    } catch (err: any) {
      const errorMsg = err.message || "Failed to update report";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const toggleReportStatus = async (
    id: number,
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      setLoading(true);
      setError(null);

      await ReportsService.toggleReportStatus(id);

      return { success: true };
    } catch (err: any) {
      const errorMsg = err.message || "Failed to toggle report status";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    updateReport,
    toggleReportStatus,
  };
}
