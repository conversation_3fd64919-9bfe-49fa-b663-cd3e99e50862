"use client";

import React from "react";
import {
  Card,
  Card<PERSON>ody,
  CardHeader,
  Input,
  Textarea,
  Switch,
  Select,
  SelectItem,
} from "@heroui/react";
import { Icon } from "@iconify/react";

interface ReportConfig {
  name: string;
  description: string;
  documentName: string;
  useEnglishFields: boolean;
  includeObservations: boolean;
}

interface ReportConfigStepProps {
  config: ReportConfig;
  onConfigChange: (config: ReportConfig) => void;
}

export default function ReportConfigStep({
  config,
  onConfigChange,
}: ReportConfigStepProps) {
  const handleInputChange = (
    field: keyof ReportConfig,
    value: string | boolean,
  ) => {
    onConfigChange({
      ...config,
      [field]: value,
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold mb-2">
          Configuración del reporte
        </h3>
        <p className="text-default-500">
          Define los datos básicos y configuraciones del reporte
        </p>
      </div>

      <Card className="w-full">
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Icon
              className="text-primary"
              icon="heroicons:document-text"
              width={24}
            />
            <h4 className="text-lg font-semibold">Información básica</h4>
          </div>
        </CardHeader>
        <CardBody className="pt-0 space-y-4">
          <Input
            label="Nombre del reporte"
            placeholder="Ingresa el nombre del reporte"
            value={config.name}
            variant="bordered"
            onValueChange={(value) => handleInputChange("name", value)}
          />

          <Textarea
            label="Descripción"
            placeholder="Describe el propósito y contenido del reporte"
            value={config.description}
            variant="bordered"
            onValueChange={(value) => handleInputChange("description", value)}
          />

          <Input
            label="Nombre del documento"
            placeholder="Nombre del archivo que se generará"
            value={config.documentName}
            variant="bordered"
            onValueChange={(value) => handleInputChange("documentName", value)}
          />
        </CardBody>
      </Card>

      <Card className="w-full">
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Icon
              className="text-primary"
              icon="heroicons:cog-6-tooth"
              width={24}
            />
            <h4 className="text-lg font-semibold">Configuraciones</h4>
          </div>
        </CardHeader>
        <CardBody className="pt-0 space-y-4">
          <div className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
            <div className="flex items-center gap-3">
              <Icon
                className="text-default-500"
                icon="heroicons:language"
                width={20}
              />
              <div>
                <p className="font-medium">Idioma de los campos</p>
                <p className="text-sm text-default-500">
                  Seleccionar el idioma para los campos
                </p>
              </div>
            </div>
            <Select
              aria-label="Seleccionar idioma"
              className="w-40"
              disableAnimation={true}
              selectedKeys={[config.useEnglishFields ? "false" : "true"]}
              variant="bordered"
              onChange={(e) =>
                handleInputChange(
                  "useEnglishFields",
                  e.target.value === "false",
                )
              }
            >
              <SelectItem key="false">English</SelectItem>
              <SelectItem key="true">Español</SelectItem>
            </Select>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
