"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export function useVerifyProjectPhase() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const verifyProjectPhase = async (
    projectId: string | number,
    phaseId: number,
  ) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.post(
        `${API_ROUTES.VERIFY_PROJET_PHASE.replace("{project_id}", String(projectId)).replace("{phase_id}", String(phaseId))}`,
      );

      if (response.status === 200) {
        return response.data;
      } else {
        setError("Failed to verify project phase");
      }
    } catch (err: any) {
      setError(err.message || "Failed to verify project phase");
    } finally {
      setLoading(false);
    }

    return null;
  };

  return {
    loading,
    error,
    verifyProjectPhase,
  };
}
