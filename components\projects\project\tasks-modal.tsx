import React, { useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  Modal<PERSON>ooter,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Chip,
} from "@heroui/react";
import { Icon } from "@iconify/react";

export interface Task {
  id: string;
  name: string;
  description: string;
  status: "En progreso" | "Ni empezada";
  dueDate: string;
}

export interface TasksModalProps {
  isOpen: boolean;
  onClose: () => void;
  tasks: any;
  onViewTask: (taskId: string) => void;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "IN_PROGRESS":
      return "warning";
    case "NOT_STARTED":
      return "danger";
    default:
      return "default";
  }
};

export const TasksModal = ({
  isOpen,
  onClose,
  tasks,
  onViewTask,
}: TasksModalProps) => {
  useEffect(() => {
    console.log("TasksModal mounted with tasks:", tasks);
  }, [tasks]);

  return (
    <Modal isOpen={isOpen} scrollBehavior="inside" size="4xl" onClose={onClose}>
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              <h2 className="text-xl font-bold">Lista de Tareas</h2>
              <p className="text-small text-default-500">
                Gestiona tus tareas de manera eficiente
              </p>
            </ModalHeader>
            <ModalBody>
              <Table
                removeWrapper
                aria-label="Tabla de tareas"
                classNames={{
                  th: "bg-default-100 text-default-800",
                  td: "py-3",
                }}
              >
                <TableHeader>
                  <TableColumn>TAREA</TableColumn>
                  <TableColumn>DESCRIPCIÓN</TableColumn>
                  <TableColumn>TIPO</TableColumn>
                  <TableColumn>FASE</TableColumn>
                  <TableColumn>SUBFASE</TableColumn>
                  <TableColumn>ESTADO</TableColumn>
                  <TableColumn>ACCIÓN</TableColumn>
                </TableHeader>
                <TableBody emptyContent="No hay tareas disponibles">
                  {tasks.fields.map((task: any) => (
                    <TableRow key={task.id}>
                      <TableCell>{task.name}</TableCell>
                      <TableCell>
                        {/* <Tooltip content={task.description}>
                          <span className="cursor-help truncate max-w-xs inline-block">
                            {task.description}
                          </span>
                        </Tooltip> */}
                        a
                      </TableCell>
                      <TableCell>
                        <Chip color="primary" size="sm" variant="flat">
                          {task.type === "SELECTION"
                            ? "Selección"
                            : task.type === "TASK"
                              ? "Tarea"
                              : task.type === "DOCUMENT"
                                ? "Documento"
                                : task.type === "TASK_WITH_SUBTASKS"
                                  ? "Subtarea"
                                  : task.type === "INFORMATIVE"
                                    ? "Informativo"
                                    : task.type}
                        </Chip>
                      </TableCell>
                      <TableCell>
                        {task.phase?.name === "INCUBADORA"
                          ? "TAKE OFF"
                          : task.phase?.name}
                      </TableCell>
                      <TableCell>{task.subphase?.name || "N/A"}</TableCell>
                      <TableCell>
                        <Chip
                          color={getStatusColor(task.status)}
                          size="sm"
                          variant="flat"
                        >
                          {task.status === "IN_PROGRESS"
                            ? "En progreso"
                            : "Ni empezada"}
                        </Chip>
                      </TableCell>
                      <TableCell>
                        <Button
                          color="primary"
                          endContent={<Icon icon="lucide:arrow-right" />}
                          variant="flat"
                          onPress={() => onViewTask(task.id)}
                        >
                          Ver tarea
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ModalBody>
            <ModalFooter>
              <Button color="danger" variant="light" onPress={onClose}>
                Cerrar
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};
