"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export function usePendingFields() {
  const [pendingFields, setPendingFields] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPendingFields = async (projectId: string | number) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(
        `${API_ROUTES.PENDING_FIELDS.replace("{project_id}", String(projectId))}`,
      );

      if (response.status === 200) {
        setPendingFields(response.data);
      } else {
        setError("Failed to fetch pending fields");
      }

      return response.data;
    } catch (err: any) {
      setError(err.message || "Failed to fetch pending fields");
    } finally {
      setLoading(false);
    }

    return [];
  };

  return {
    pendingFields,
    loading,
    error,
    fetchPendingFields,
  };
}
