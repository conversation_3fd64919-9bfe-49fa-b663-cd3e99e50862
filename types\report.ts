export interface ReportConfig {
  name: string;
  description: string;
  documentName: string;
  useEnglishFields: boolean;
  includeObservations: boolean;
}

export interface ApiReport {
  [x: string]: string;
  id: number;
  name: string;
  description: string;
  document_name: string;
  observations: boolean;
  use_english: boolean;
  filters: Record<string, any>;
  active: boolean;
  created_at: string;
  fields?: {
    id: string;
    name: string;
  }[];
}

export interface ReportField {
  id: number;
  name: string;
  type: string;
  subphase: {
    id: number;
    name: string;
    phase: {
      id: number;
      name: string;
    };
  };
}

export interface FieldTypeFilter {
  statuses: ("completed" | "not_completed" | "in_progress")[];
  includeObservations: boolean;
}

export interface ReportFilter {
  field_type: string;
  filter_options: ("completed" | "not_completed" | "in_progress")[];
  include_observations: boolean;
}

export interface ReportDetailResponse {
  id: number;
  name: string;
  description: string;
  document_name: string;
  observations: boolean;
  use_english: boolean;
  filters: Record<string, any>;
  active: boolean;
  created_at: string;
  fields: {
    id: string;
    name: string;
  }[];
}

export interface CreateReportData {
  reportConfig: ReportConfig;
  selectedFields: string[];
  fieldFilters: Record<string, any>;
}

export interface UpdateReportData {
  name?: string;
  description?: string;
  document_name?: string;
  observations?: boolean;
  use_english?: boolean;
  filters?: Record<string, any>;
  active?: boolean;
}

export interface ReportData {
  name: string;
  description: string;
  documentName: string;
  useEnglishFields: boolean;
  includeObservations: boolean;
}
