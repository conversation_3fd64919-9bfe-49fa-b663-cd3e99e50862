"use client";
import React from "react";
import { <PERSON>, <PERSON>Header, CardBody } from "@heroui/react";
import { useTheme } from "next-themes";

import { getPhaseStyleText } from "../primitives";

import { Project } from "@/types/projects";

const PhaseCountCard: React.FC<{ projects: Project[]; phase: string }> = ({
  projects,
  phase,
}) => {
  const { theme } = useTheme();
  const count =
    phase === "TOTAL"
      ? projects.length
      : projects.filter((p) => p.fase === phase).length;

  return (
    <Card
      isPressable
      className={`w-28 h-18 rounded-md transition-all hover:scale-105 ${getPhaseStyleText(phase, theme === "dark")}`}
    >
      <CardHeader className="flex justify-center pb-0">
        <h5 className="font-bold text-xs tracking-wide">{typeof phase === "string" ? phase.toLowerCase() === "incubadora" ? "TAKE OFF" : phase : phase}</h5>
      </CardHeader>
      <CardBody className="flex justify-center items-center pt-0">
        <p className="text-xl font-bold">{count}</p>
      </CardBody>
    </Card>
  );
};

export default PhaseCountCard;
